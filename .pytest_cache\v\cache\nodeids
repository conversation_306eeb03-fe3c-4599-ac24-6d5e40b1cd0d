["tests/test_developer_agent.py::TestCodeGeneration::test_code_file_creation", "tests/test_developer_agent.py::TestCodeGeneration::test_test_suite_creation", "tests/test_developer_agent.py::TestDeveloperAgent::test_code_generation_context_creation", "tests/test_developer_agent.py::TestDeveloperAgent::test_developer_agent_initialization", "tests/test_developer_agent.py::TestDeveloperAgent::test_error_handling", "tests/test_developer_agent.py::TestDeveloperAgent::test_input_validation_missing_tech_stack", "tests/test_developer_agent.py::TestDeveloperAgent::test_input_validation_success", "tests/test_developer_agent.py::TestDeveloperAgent::test_project_structure_generation", "tests/test_developer_agent.py::TestDeveloperAgent::test_technology_stack_processing", "tests/test_developer_agent.py::TestQualityGates::test_code_quality_enum", "tests/test_developer_agent.py::TestQualityGates::test_project_type_enum", "tests/test_orchestrator.py::TestAgentExecution::test_developer_agent_execution"]